('C:\\Users\\<USER>\\Desktop\\clearLogs\\build\\stop_sqlserver\\stop_sqlserver.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\build\\stop_sqlserver\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\build\\stop_sqlserver\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\build\\stop_sqlserver\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\build\\stop_sqlserver\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\build\\stop_sqlserver\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\build\\stop_sqlserver\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\SeaPython\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\SeaPython\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('stop_sqlserver',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\stop_sqlserver.py',
   'PYSOURCE'),
  ('python313.dll', 'D:\\SeaPython\\python313.dll', 'BINARY'),
  ('unicodedata.pyd', 'D:\\SeaPython\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\SeaPython\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\SeaPython\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\SeaPython\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\SeaPython\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\SeaPython\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\SeaPython\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\SeaPython\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\SeaPython\\DLLs\\libcrypto-3.dll', 'BINARY'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\build\\stop_sqlserver\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
