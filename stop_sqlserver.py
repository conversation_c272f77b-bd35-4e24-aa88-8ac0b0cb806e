#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Server MSSQLSERVER服务停止脚本
静默执行，无提示，无弹框
"""

import subprocess
import sys
import os


def stop_sqlserver_service():
    """
    停止SQL Server MSSQLSERVER服务
    静默执行，不显示任何输出
    """
    try:
        # 使用net stop命令停止MSSQLSERVER服务
        # /y参数用于自动确认停止依赖服务
        cmd = ["net", "stop", "MSSQLSERVER", "/y"]
        
        # 静默执行，不显示任何输出
        result = subprocess.run(
            cmd,
            capture_output=True,  # 捕获输出，不显示在控制台
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0  # Windows下不创建窗口
        )
        
        # 静默退出，不输出任何信息
        sys.exit(0)
        
    except Exception:
        # 即使出错也静默退出
        sys.exit(1)


if __name__ == "__main__":
    stop_sqlserver_service()
