(['C:\\Users\\<USER>\\Desktop\\clearLogs\\stop_sqlserver.py'],
 ['C:\\Users\\<USER>\\Desktop\\clearLogs'],
 [],
 [('D:\\SeaPython\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\SeaPython\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\SeaPython\\Lib\\site-packages\\_pyinstaller_hooks_contrib', -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\SeaPython\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('stop_sqlserver',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\stop_sqlserver.py',
   'PYSOURCE')],
 [('zipfile', 'D:\\SeaPython\\Lib\\zipfile\\__init__.py', 'PYMODULE'),
  ('zipfile._path',
   'D:\\SeaPython\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\SeaPython\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib', 'D:\\SeaPython\\Lib\\pathlib\\__init__.py', 'PYMODULE'),
  ('pathlib._local', 'D:\\SeaPython\\Lib\\pathlib\\_local.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\SeaPython\\Lib\\urllib\\parse.py', 'PYMODULE'),
  ('urllib', 'D:\\SeaPython\\Lib\\urllib\\__init__.py', 'PYMODULE'),
  ('ipaddress', 'D:\\SeaPython\\Lib\\ipaddress.py', 'PYMODULE'),
  ('glob', 'D:\\SeaPython\\Lib\\glob.py', 'PYMODULE'),
  ('fnmatch', 'D:\\SeaPython\\Lib\\fnmatch.py', 'PYMODULE'),
  ('pathlib._abc', 'D:\\SeaPython\\Lib\\pathlib\\_abc.py', 'PYMODULE'),
  ('contextlib', 'D:\\SeaPython\\Lib\\contextlib.py', 'PYMODULE'),
  ('argparse', 'D:\\SeaPython\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\SeaPython\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\SeaPython\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\SeaPython\\Lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'D:\\SeaPython\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\SeaPython\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'D:\\SeaPython\\Lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\SeaPython\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\SeaPython\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\SeaPython\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message', 'D:\\SeaPython\\Lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'D:\\SeaPython\\Lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\SeaPython\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', 'D:\\SeaPython\\Lib\\email\\quoprimime.py', 'PYMODULE'),
  ('string', 'D:\\SeaPython\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\SeaPython\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\SeaPython\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\SeaPython\\Lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'D:\\SeaPython\\Lib\\email\\generator.py', 'PYMODULE'),
  ('random', 'D:\\SeaPython\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\SeaPython\\Lib\\statistics.py', 'PYMODULE'),
  ('decimal', 'D:\\SeaPython\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\SeaPython\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'D:\\SeaPython\\Lib\\contextvars.py', 'PYMODULE'),
  ('fractions', 'D:\\SeaPython\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\SeaPython\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\SeaPython\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging', 'D:\\SeaPython\\Lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'D:\\SeaPython\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\SeaPython\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'D:\\SeaPython\\Lib\\dataclasses.py', 'PYMODULE'),
  ('_compat_pickle', 'D:\\SeaPython\\Lib\\_compat_pickle.py', 'PYMODULE'),
  ('bisect', 'D:\\SeaPython\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\SeaPython\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\SeaPython\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\SeaPython\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset', 'D:\\SeaPython\\Lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'D:\\SeaPython\\Lib\\email\\encoders.py', 'PYMODULE'),
  ('email.base64mime', 'D:\\SeaPython\\Lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\SeaPython\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header', 'D:\\SeaPython\\Lib\\email\\header.py', 'PYMODULE'),
  ('email.errors', 'D:\\SeaPython\\Lib\\email\\errors.py', 'PYMODULE'),
  ('email.utils', 'D:\\SeaPython\\Lib\\email\\utils.py', 'PYMODULE'),
  ('socket', 'D:\\SeaPython\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\SeaPython\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr', 'D:\\SeaPython\\Lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'D:\\SeaPython\\Lib\\calendar.py', 'PYMODULE'),
  ('datetime', 'D:\\SeaPython\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime', 'D:\\SeaPython\\Lib\\_pydatetime.py', 'PYMODULE'),
  ('_strptime', 'D:\\SeaPython\\Lib\\_strptime.py', 'PYMODULE'),
  ('quopri', 'D:\\SeaPython\\Lib\\quopri.py', 'PYMODULE'),
  ('typing', 'D:\\SeaPython\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'D:\\SeaPython\\Lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\SeaPython\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\SeaPython\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\SeaPython\\Lib\\tempfile.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\SeaPython\\Lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\SeaPython\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('email', 'D:\\SeaPython\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'D:\\SeaPython\\Lib\\email\\parser.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\SeaPython\\Lib\\email\\feedparser.py', 'PYMODULE'),
  ('json', 'D:\\SeaPython\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'D:\\SeaPython\\Lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'D:\\SeaPython\\Lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'D:\\SeaPython\\Lib\\json\\scanner.py', 'PYMODULE'),
  ('__future__', 'D:\\SeaPython\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\SeaPython\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\SeaPython\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\SeaPython\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\SeaPython\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\SeaPython\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\SeaPython\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression', 'D:\\SeaPython\\Lib\\_compression.py', 'PYMODULE'),
  ('bz2', 'D:\\SeaPython\\Lib\\bz2.py', 'PYMODULE'),
  ('threading', 'D:\\SeaPython\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'D:\\SeaPython\\Lib\\_threading_local.py', 'PYMODULE'),
  ('struct', 'D:\\SeaPython\\Lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\SeaPython\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\SeaPython\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\SeaPython\\Lib\\gzip.py', 'PYMODULE'),
  ('importlib.util', 'D:\\SeaPython\\Lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\SeaPython\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\SeaPython\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\SeaPython\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata', 'D:\\SeaPython\\Lib\\_opcode_metadata.py', 'PYMODULE'),
  ('ast', 'D:\\SeaPython\\Lib\\ast.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\SeaPython\\Lib\\tracemalloc.py', 'PYMODULE'),
  ('_py_abc', 'D:\\SeaPython\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_colorize', 'D:\\SeaPython\\Lib\\_colorize.py', 'PYMODULE'),
  ('stringprep', 'D:\\SeaPython\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\SeaPython\\Lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'D:\\SeaPython\\Lib\\signal.py', 'PYMODULE')],
 [('python313.dll', 'D:\\SeaPython\\python313.dll', 'BINARY'),
  ('unicodedata.pyd', 'D:\\SeaPython\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'D:\\SeaPython\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\SeaPython\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'D:\\SeaPython\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\SeaPython\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\SeaPython\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\SeaPython\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('VCRUNTIME140.dll', 'D:\\SeaPython\\VCRUNTIME140.dll', 'BINARY'),
  ('libcrypto-3.dll', 'D:\\SeaPython\\DLLs\\libcrypto-3.dll', 'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\clearLogs\\build\\stop_sqlserver\\base_library.zip',
   'DATA')])
